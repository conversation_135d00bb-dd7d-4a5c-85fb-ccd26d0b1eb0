import { Tooltip } from "antd";
import React from "react";
import { deviceConfigs } from "../Components/content";

const DeviceButtons = ({
  // deviceConfig,
  previewMode,
  setPreviewMode,
}) => {
  const deviceConfig = deviceConfigs(previewMode);
  return (
    <>
      <div className="tw-flex tw-items-center tw-space-x-1 tw-rounded-lg tw-hidden md:tw-flex tw-mb-1">
        {Object.entries(deviceConfig)?.map(([key, config]) => (
          <Tooltip key={key} title={`${config.label} (${config.description})`}>
            <button
              type="button"
              onClick={() => setPreviewMode(key)}
              className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                previewMode === key
                  ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                  : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
              }`}
            >
              {config.icon}
            </button>
          </Tooltip>
        ))}
      </div>
    </>
  );
};

export default DeviceButtons;
