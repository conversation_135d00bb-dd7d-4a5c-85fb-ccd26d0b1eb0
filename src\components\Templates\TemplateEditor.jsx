import React, { useState, useEffect } from "react";
import Header from "../Layout/Header";
import { Save, X, Plus, Trash2, FileText, GripVertical } from "lucide-react";
// import useHttp from "../../hooks/use-http"; // Commented for future API use
import useStorage from "../../hooks/use-storage"; // Using JSON storage
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import TemplateTabList from "./Component/TemplateTabList";

const TemplateEditor = React.memo(({ template, pages, onCancel }) => {
  // const api = useHttp(); // Commented for future API use
  const api = useStorage(); // Using JSON storage
  const [formData, setFormData] = useState({
    name: "", // Template name
    description: "",
    full_template_content: "", // New field: template preview full code
    templateComponentList: [], // New field: template preview page list
    pages: [], // Existing field for backward compatibility
    content: [], // New field: array of json
  });
  // const [availablePages, setAvailablePages] = useState([]);
  const [saving, setSaving] = useState(false);
  console.log(template, "template", formData, pages);

  useEffect(() => {
    if (template) {
      console.log(template, "template");
      setFormData(() => ({
        ...template,
        name: template.name || "",
        description: template.description || "",
        full_template_content: template.full_template_content || "", // New field
        templateComponentList: template.templateComponentList || [], // New field
        pages: template.pages || [],
        content: template.content || [], // New field
      }));
    } else {
      // Initialize with empty data for new templates
      setFormData(() => ({
        name: "",
        description: "",
        full_template_content: "",
        templateComponentList: [],
        pages: [],
        content: [],
      }));
    }

    // setAvailablePages(pages || []);
  }, [template?.id]); // Only depend on template.id to avoid unnecessary re-renders

  const handleSubmit = async (formData, extraCall) => {
    // e.preventDefault();
    setSaving(true);
    console.log(formData, "final submit");
    const apiConfig = template
      ? apiGenerator(CONSTANTS.API.templates.update, { id: template.id })
      : CONSTANTS.API.templates.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Template saved successfully:", res);
        // Update formData with response data if available
        setFormData((prevData) => ({ ...prevData, ...res }));
        extraCall && extraCall(res);
        setSaving(false);
      },
      formData,
      template
        ? "Template updated successfully!"
        : "Template created successfully!",
      (error) => {
        console.error("Error saving template:", error);
        setSaving(false);
      }
    );
  };

  return (
    <>
      <div className="  tw-mx-auto">
        <TemplateTabList
          pages={pages}
          formData={formData}
          setFormData={setFormData}
          template={template}
          onCancel={onCancel}
          handleSubmit={handleSubmit}
          saving={saving}
        />
      </div>
    </>
  );
});

export default TemplateEditor;
