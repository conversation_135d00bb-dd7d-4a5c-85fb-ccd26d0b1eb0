import React from 'react';
import { Input } from "antd";
import JsonContentCollapse from "../components/common/JsonContentCollapse";

const { TextArea } = Input;
export const apiGenerator = (apiObject, exchangePair = {}, join = null) => {
    const apiObj = { ...apiObject };
    if (Object.keys(exchangePair).length) {
        Object.keys(exchangePair).forEach((el) => {
            apiObj.endpoint = apiObj.endpoint.replace(`:${el}`, exchangePair[el]);
        });
    }

    if (join) {
        apiObj.endpoint = `${apiObj.endpoint}${join}`;
    }
    return apiObj;
};

export const generateRoutes = (routes = [], role) => {
    let Routes = [];

    routes?.forEach((route) => {
        const mainRoute = { ...route };
        // nestedBasePath = nestedBasePath.trim().replace("//", "/");
        // const menuItem = {
        //   id: nestedBasePath,
        //   icon: route?.icon,
        //   label: route?.label,
        //   key: nestedBasePath,
        // };

        if (route?.children && route?.children?.length > 0) {
            // Recursive call for nested children

            const children = generateRoutes(route?.children, role);
            if (children.length) mainRoute.children = children;
        }
        if (!route?.Role || route?.Role?.includes(role)) {
            Routes?.push(mainRoute);
        } else if (
            mainRoute?.children?.length &&
            (!route?.Role || route?.Role?.includes(role))
        ) {
            Routes?.push(...mainRoute.children);
        }
    });
    return Routes;
};


// export const buildItemList = (data, expandedAll, parentKey, handleDelete, handleAddItem) => {
//     if (Array.isArray(data)) {
//         // If data is an array
//         console.log(data)
//         return data.map((item, index) => ({
//             key: `${parentKey}_pos_${index}`,
//             label: `Position ${index + 1}`,
//             children: React.createElement(JsonContentCollapse, {
//                 expanded: expandedAll,
//                 onDeleteItem: (it) => handleDelete(parentKey, index, it.key),
//                 itemList: buildItemList(item, expandedAll, parentKey, handleDelete, handleAddItem)
//             })
//         }));
//     } else if (typeof data === "object" && data !== null) {
//         // If data is an object
//         return Object.keys(data).map((key) => ({
//             key,
//             label: key,
//             isDelete: true,
//             onDelete: () => handleDelete(parentKey, null, key),
//             children: typeof data[key] === "object"
//                 ? React.createElement(JsonContentCollapse, {
//                     expanded: expandedAll,
//                     itemList: buildItemList(
//                         data[key],
//                         expandedAll,
//                         key,
//                         handleDelete,
//                         handleAddItem
//                     ),
//                 })
//                 : React.createElement(TextArea, { rows: 4, value: data[key], readOnly: true }),
//         }));
//     } else {
//         // Primitive values
//         return [
//             {
//                 key: parentKey,
//                 label: parentKey,
//                 children: React.createElement(TextArea, { rows: 4, value: data, readOnly: true }),
//             },
//         ];
//     }
// };


export const autoDetectRepeatPlaceHolder = (code) => {
    const content = code;
    // if (!content) return;
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = [];
    let match;

    while ((match = regex.exec(content)) !== null) {
        if (!matches.includes(match[1])) {
            matches.push(match[1]);
        }
    }
    return matches;
    // ...formData.placeholders,
    // setFormData({
    //   ...formData,
    //   placeholders: [...new Set([...matches])],
    // });
};

// =================================Collapse functinlity =========================================

export const updateAtPath = (obj, path, updater) => {
    if (path.length === 0) return updater(obj);

    const [head, ...rest] = path;

    if (Array.isArray(obj)) {
        const clone = [...obj];
        clone[head] = updateAtPath(obj[head], rest, updater);
        return clone;
    } else if (typeof obj === "object" && obj !== null) {
        return {
            ...obj,
            [head]: updateAtPath(obj[head], rest, updater),
        };
    }
    return obj;
};
export const addAtPath = (obj, path) =>
    updateAtPath(obj, path, (arr) => {
        if (!Array.isArray(arr) || arr.length === 0) {
            message.warning("Cannot add item: reference structure not found.");
            return arr;
        }
        const referenceKeys = Object.keys(arr[0] || {});
        const newItem = referenceKeys.reduce(
            (acc, k) => ({ ...acc, [k]: "" }),
            {}
        );
        return [...arr, newItem];
    });
export const deleteAtPath = (obj, path, fieldKey = null) =>
    updateAtPath(obj, path, (target) => {
        if (Array.isArray(target)) {
            const index = path[path.length - 1];
            if (typeof index === "number" && index < target.length) {
                return target.filter((_, i) => i !== index);
            }
        } else if (typeof target === "object" && target !== null) {
            if (fieldKey) {
                const { [fieldKey]: _, ...rest } = target;
                return rest;
            }
        }
        return target;
    });