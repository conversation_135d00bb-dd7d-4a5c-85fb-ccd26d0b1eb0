import React, { useState, useEffect, useRef } from "react";
import { Input, Select, Switch, Tooltip, Button, message } from "antd";
import {
  ChevronLeft,
  GripVertical,
  Search,
  Loader2,
  Plus,
  Trash2,
  Eye,
  FileText,
} from "lucide-react";
import { useDrag, useDrop, DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
// import useHttp from "../../../hooks/use-http"; // Commented for future API use
import useStorage from "../../../hooks/use-storage"; // Using JSON storage

import { generateGlobalPreviewHTML } from "../../Components/content";
import TemplateLibrary from "./TemplateLibrary";
import TemplatePreview from "./TemplatePreview";
import TemplateStructure from "./TemplateStructure";
import { CONSTANTS } from "../../../util/constant/CONSTANTS";
import { useNavigate } from "react-router-dom";

// DND Types
const DND_TYPES = {
  PAGE_ITEM: "PAGE_ITEM",
};

// Device sizes for responsive preview
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const StructureTab = ({
  pages = [],
  formData,
  setFormData,
  template,
  onCancel,
  handleSubmit,
  saving,
}) => {
  // const api = useHttp(); // Commented for future API use
  const api = useStorage(); // Using JSON storage
  const [isPageLibraryOpen, setIsPageLibraryOpen] = useState(true);
  const [isTemplateStructureOpen, setIsTemplateStructureOpen] = useState(true);
  const [selectedPageId, setSelectedPageId] = useState(null);
  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
  });

  // Responsive detection
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setScreenSize({
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
      });
    };

    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // Fetch pages on component mount
  // useEffect(() => {
  // setIsLoading(true);
  // setPagesList(pages || []);
  // api.sendRequest(
  //   CONSTANTS.API.pages.get,
  //   (res) => {
  //     console.log("Pages fetched:", res);
  //     setPages(res);
  //     setIsLoading(false);
  //   },
  //   null,
  //   null,
  //   (error) => {
  //     console.error("Error fetching pages:", error);
  //     setIsLoading(false);
  //   }
  // );
  // }, [pages]);

  // Initialize template pages if not exists - only run once when formData is first loaded
  // useEffect(() => {
  //   if (!formData.pages && formData.name !== undefined) {
  //     setFormData((prevData) => ({ ...prevData, pages: [] }));
  //   }
  // }, [formData.name]); // Only depend on formData.name to avoid infinite loops

  // // Scale calculation function for preview
  // const recalcScale = () => {
  //   if (!containerRef.current) return;
  //   const bounds = containerRef.current.getBoundingClientRect();
  //   const availableWidth = bounds.width - 30;
  //   const availableHeight = bounds.height - 30;
  //   const widthScale = availableWidth / deviceWidth;
  //   const heightScale = availableHeight / deviceHeight;
  //   setScale(Math.min(widthScale, heightScale, 1));
  // };

  // useEffect(() => {
  //   recalcScale();
  //   const resizeObserver = new ResizeObserver(recalcScale);
  //   if (containerRef.current) {
  //     resizeObserver.observe(containerRef.current);
  //   }
  //   return () => resizeObserver.disconnect();
  // }, [deviceWidth, deviceHeight]);

  // Global onChange handler for all component fields
  const handlePagesFieldChange = (index, field, value, extraData = {}) => {
    const updated = [...formData.pages];
    updated[index] = { ...updated[index], [field]: value, ...extraData };
    // console.log("Updated pages:", updated);
    setFormData((prevData) => ({
      ...prevData,
      pages: updated,
      // pageComponentList: updated,
    }));
  };
  // console.log(formData, "formData");
  // Remove page from template
  const removePageFromTemplate = (pageId) => {
    const updatedPages = formData.pages?.filter((p) => p.order != pageId) || [];
    setFormData({ ...formData, pages: updatedPages });
    if (selectedPageId === pageId) {
      setSelectedPageId(null);
    }
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    // console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...formData?.pages];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setFormData((prevData) => ({
      ...prevData,
      pages: updatedComponents,
      // pages: updatedComponents,
    }));
  };

  // Update page in template
  const updatePageInTemplate = (pageId, updates) => {
    const updatedPages =
      formData.pages?.map((p) =>
        p.id === pageId ? { ...p, ...updates } : p
      ) || [];
    setFormData({ ...formData, pages: updatedPages });
  };

  // Save template structure
  const handleSaveStructure = async () => {
    try {
      // Generate full template content from current structure
      const fullTemplateContent = generateTemplateHTML();

      // Create templateComponentList from current pages
      const templateComponentList =
        formData.pages?.map((page, index) => ({
          version: "v1",
          url: page.slug || `/${page.name.toLowerCase().replace(/\s+/g, "-")}`,
          repeator: "single",
          position: index,
          showNavbar: page.showNavbar || false,
          navPosition: page.navPosition || index,
          placeHolder: page.pagePlaceHolder || [],
        })) || [];

      // Update formData with generated content
      const updatedFormData = {
        ...formData,
        full_template_content: fullTemplateContent,
        templateComponentList: templateComponentList,
        content: formData.content || [],
      };
      handleSubmit(updatedFormData);
      // setFormData(updatedFormData);
      // api.sendRequest(CONSTANTS.API.templates.get, (res) => {
      //   // setTemplates(res);
      // });
      // If template exists, save it
      // if (template && template.id) {
      //   api
      //     .directOperation("templates", "update", template.id, updatedFormData)
      //     .then((res) => {
      //       console.log("Template structure saved successfully:", res);
      //       message.success("Template structure saved successfully!");
      //     })
      //     .catch((error) => {
      //       console.error("Error saving template structure:", error);
      //       message.error(
      //         "Failed to save template structure. Please try again."
      //       );
      //     });
      // } else {
      //   message.info(
      //     "Please save the template details first before saving structure."
      //   );
      // }
    } catch (error) {
      console.error("Error generating template structure:", error);
      message.error("Failed to generate template structure. Please try again.");
    }
  };

  // Generate template HTML from current structure
  const generateTemplateHTML = () => {
    if (!formData.pages || formData.pages.length === 0) {
      return '<div class="template-placeholder">No pages added to template</div>';
    }

    const pagesHTML = formData.pages
      .map((page, index) => {
        return `
        <div class="template-page" data-page-id="${
          page.id
        }" data-position="${index}">
          <div class="page-content">
            ${
              page.full_page_content ||
              `<div class="page-placeholder">Page: ${page.name}</div>`
            }
          </div>
        </div>
      `;
      })
      .join("\n");

    return `
      <div class="template-container">
        <div class="template-pages">
          ${pagesHTML}
        </div>
      </div>
    `;
  };

  // Get filtered pages (exclude already added pages)
  const availablePages = pages?.filter(
    (page) => !formData.pages?.some((tp) => tp.id === page.id)
  );

  const onCancelHandler = () => {
    onCancel();
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {screenSize?.isMobile &&
          (isPageLibraryOpen || isTemplateStructureOpen) && (
            <div
              className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
              onClick={() => {
                setIsPageLibraryOpen(false);
                setIsTemplateStructureOpen(false);
              }}
            />
          )}

        <TemplateLibrary
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          screenSize={screenSize}
          availablePages={availablePages}
        />

        {/* Center Preview Area */}
        <TemplatePreview
          isPageLibraryOpen={isPageLibraryOpen}
          setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          formData={formData}
          setFormData={setFormData}
          pages={pages}
          handleSave={handleSaveStructure}
          onCancel={onCancelHandler}
          saving={saving}
        />

        {/* Right Sidebar - Template Structure */}

        <TemplateStructure
          isTemplateStructureOpen={isTemplateStructureOpen}
          setIsTemplateStructureOpen={setIsTemplateStructureOpen}
          pageData={formData}
          formData={formData}
          setFormData={setFormData}
          removePageFromTemplate={removePageFromTemplate}
          onComponentFieldChange={handlePagesFieldChange}
          moveComponent={moveComponent}
          screenSize={screenSize}
        />
      </div>
    </DndProvider>
  );
};

export default StructureTab;
