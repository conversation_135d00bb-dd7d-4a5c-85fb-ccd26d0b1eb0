import { Button, Collapse, Input, Row, Col, message } from "antd";
import {
  ChevronDown,
  ChevronUp,
  Upload as UploadIcon,
  ExpandIcon,
  Minimize2,
} from "lucide-react";
import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import TemplatePreview from "./TemplatePreview";
import SearchBar from "../../common/SearchBar";
import JsonContentCollapse from "../../common/JsonContentCollapse";
import ContentCollapseBar from "./ContentCollapseBar";

const { Panel } = Collapse;
const { TextArea } = Input;

const ContentTab = ({
  formData,
  setFormData,
  onCancel,
  handleSubmit,
  saving,
  pages,
}) => {
  // Local content JSON state (example data for testing functionality)
  const [placeholder, setPlaceholder] = useState("");
  console.log(formData, "formData");
  useEffect(() => {
    setPlaceholder(formData?.placeholders || []);
    if (formData?.pages?.length > 0) {
      setContentJSON((prev) => {
        let keydata = {};

        formData.pages.forEach((page) => {
          if (page?.pagePlaceHolder?.length > 0) {
            let categoryData = {};

            page.pagePlaceHolder.forEach((category) => {
              let placeholdersData = {};

              // Normal placeholders
              category?.placeholders?.forEach((ph) => {
                placeholdersData[ph] = "";
              });

              // Handle repeated component
              if (
                category?.repeatedComponentId &&
                category?.repeatedComponent
              ) {
                const repeatedKey = category?.repeatedComponentName?.[0];
                if (repeatedKey) {
                  const repeatedPlaceholders =
                    category.repeatedComponent.placeholders || [];

                  // initialize with a single empty item; user can add more dynamically
                  placeholdersData[repeatedKey] = [
                    repeatedPlaceholders.reduce((acc, ph) => {
                      acc[ph] = "";
                      return acc;
                    }, {}),
                  ];
                }
              }

              categoryData[category?.categoryName] = placeholdersData;
            });

            keydata[page?.name] = categoryData;
          }
        });

        console.log("Final keydata:", keydata);

        return {
          ...prev,
          ...keydata,
        };
      });
    }
  }, []);

  const [contentJSON, setContentJSON] = useState({
    // "Hero Section": {
    //   $section_title: "Your Adventure Awaits!",
    //   $section_content:
    //     "Discover and book bus tickets to your favorite destinations with ease. We offer the best prices and a comfortable journey.",
    // },
    // Popular_Bus_Routes: [
    //   {
    //     $title: "Coimbatore Buses",
    //     $location: "Manali, Jaipur",
    //   },
    //   {
    //     $title: "Mountain Express",
    //     $location: "Shimla, Ooty",
    //   },
    //   {
    //     $title: "Coastal Rider",
    //     $location: "Goa, Pondicherry",
    //   },
    // ],
  });

  console.log(formData, "formData.pages");
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden">
        <TemplatePreview
          isPageLibraryOpen={true}
          //   setIsPageLibraryOpen={setIsPageLibraryOpen}
          isTemplateStructureOpen={true}
          //   setIsTemplateStructureOpen={() => {}} // Disable toggle
          formData={formData}
          setFormData={setFormData}
          pages={pages}
          handleSave={handleSubmit}
          onCancel={onCancel}
          saving={saving}
          isDrop={false} // Disable drop functionality
          contentJSON={contentJSON}
        />

        {/* Right Side - Content Editor */}
        <div className="tw-h-full tw-flex tw-flex-col tw-bg-white tw-flex-1">
          {/* Header */}
          <ContentCollapseBar
            contentJSON={contentJSON}
            setContentJSON={setContentJSON}
            saving={saving}
            formData={formData}
            pages={pages}
          />
        </div>
      </div>
    </DndProvider>
  );
};

export default ContentTab;
