import { Input, Select, Tooltip } from "antd";
import {
  ChevronLeft,
  FileText,
  GripVertical,
  Loader2,
  Search,
} from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { useDrag } from "react-dnd";
import SearchBar from "../../common/SearchBar";

const TemplateLibrary = ({
  isPageLibraryOpen,
  setIsPageLibraryOpen,
  screenSize,

  availablePages,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const filteredPages = useMemo(() => {
    return availablePages?.filter((page) =>
      searchTerm
        ? page?.name?.toLowerCase().includes(searchTerm.toLowerCase())
        : true
    );
  }, [availablePages, searchTerm]);
  console.log(availablePages, "availablePages", filteredPages, searchTerm);

  const handleGlobalFieldChange = (index, field, value) => {
    // if (onComponentFieldChange) {
    //   onComponentFieldChange(index, field, value);
    // }
  };
  // Draggable Page Item Component
  const PageLibraryItem = ({ page }) => {
    const [{ isDragging }, drag] = useDrag(
      () => ({
        type: "PAGE_ITEM",
        item: () => {
          console.log("Starting page drag:", page.name);
          return { page };
        },
        collect: (monitor) => ({ isDragging: monitor.isDragging() }),
        end: (_, monitor) => {
          if (monitor.didDrop()) {
            console.log("Page dropped successfully:", page.name);
          }
        },
      }),
      [page]
    );

    return (
      <div
        ref={drag}
        className="tw-bg-white tw-rounded-lg  tw-border tw-border-gray-200 tw-hover:tw-border-gray-300 tw-transition-colors tw-p-3 tw-cursor-move tw-select-none"
        style={{ opacity: isDragging ? 0.6 : 1 }}
      >
        <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
          <div className="tw-flex-1 tw-flex tw-flex-col  tw-gap-3">
            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
              {page.name}
            </p>
            <Select
              size="large"
              placeholder="V1"
              value={page?.version || "v1"}
              onChange={(value) =>
                handleGlobalFieldChange("1", "version", value)
              }
              onMouseDown={(e) => e.stopPropagation()}
              onFocus={(e) => e.stopPropagation()}
              className="tw-w-12 tw-h-3 tw-text-sm version-class"
              styles={{
                optionFontSize: "5px",
              }}
              style={{
                width: 54, // Adjust the width as needed
                height: 30,
                borderRadius: "100px", // Creates the pill shape
                fontSize: "12px",
              }}
              options={[
                { value: +"1", label: "v1" },
                { value: +"2", label: "v2" },
                { value: +"3", label: "v3" },
              ]}
            />
            {/* <p className="tw-text-xs tw-text-gray-500">/{page.slug}</p> */}
          </div>
          <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Left Sidebar - Page Library */}
      <div
        className={`tw-bg-white tw-border-r tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isPageLibraryOpen
            ? screenSize?.isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : screenSize?.isTablet
              ? "tw-w-64"
              : "tw-w-80"
            : "tw-w-0 tw-overflow-hidden"
        } ${screenSize?.isMobile && isPageLibraryOpen ? "tw-shadow-2xl" : ""}`}
      >
        {isPageLibraryOpen && (
          <>
            <div className="tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
              <div className="tw-p-3 md:tw-p-4">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                  Page Library
                </h3>
                <p className="tw-text-sm tw-text-gray-500">
                  Drag pages to build your Template
                </p>
              </div>
              {!screenSize?.isMobile && (
                <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                  <Tooltip title="Hide Page Library">
                    <button
                      onClick={() => setIsPageLibraryOpen(false)}
                      className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                    >
                      <ChevronLeft size={30} />
                    </button>
                  </Tooltip>
                </div>
              )}
            </div>

            <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4">
              <SearchBar handleSearch={(e) => setSearchTerm(e)} />

              <div className="tw-space-y-3">
                {filteredPages?.map((page) => (
                  <PageLibraryItem key={page.id} page={page} />
                ))}
                {filteredPages?.length == 0 && (
                  <div className="tw-text-center tw-py-8">
                    <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                    <p className="tw-text-gray-500 tw-mb-2">No pages found</p>
                    <p className="tw-text-sm tw-text-gray-400">
                      {searchTerm
                        ? "Try adjusting your search"
                        : "Create pages first to add them to templates"}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default TemplateLibrary;
