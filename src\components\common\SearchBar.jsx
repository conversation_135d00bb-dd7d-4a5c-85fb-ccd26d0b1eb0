import { Input } from "antd";
import { Loader2, Search } from "lucide-react";
import React, { useEffect, useState } from "react";

const SearchBar = ({ handleSearch }) => {
  const [searchTerm, setSearchTerm] = useState(null);
  const [isSearching, setIsSearching] = useState(false);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm != null) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        handleSearch(searchTerm);
        // setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      //   setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);
  return (
    <>
      <Input
        placeholder="Search pages..."
        size="middle"
        prefix={
          isSearching ? (
            <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
          ) : (
            <Search className="tw-w-4 tw-h-4 tw-text-gray-400" />
          )
        }
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="tw-rounded-lg tw-mb-4"
        allowClear
      />
    </>
  );
};

export default SearchBar;
