import { Input, <PERSON>lt<PERSON>, <PERSON><PERSON>se, Spin, Select, But<PERSON> } from "antd";
import {
  ChevronLeft,
  GripVertical,
  Trash2,
  Search,
  ChevronDown,
  ChevronUp,
  Loader2,
  X,
} from "lucide-react";
import React, { useRef, useState, useEffect } from "react";
import { useDrag, useDrop } from "react-dnd";
import { DND_TYPES } from "../../../util/content";
import { autoDetectRepeatPlaceHolder } from "../../../util/functions";

// const ITEM_TYPE = "STRUCTURE_ITEM";

const PageStructure = ({
  isStructureOpen,
  setIsStructureOpen,
  pageData,
  components,
  handleCssChange,
  removeComponentFromPage,
  moveComponent,
  onComponentFieldChange, // Global onChange handler
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Simple move function - no debouncing, immediate state update
  //   const moveComponent = useCallback(
  //     (dragIndex, hoverIndex) => {
  //       console.log(`Moving component from ${dragIndex} to ${hoverIndex}`);

  //       setPageData((prevData) => {
  //         const newComponents = [...prevData.components];
  //         const draggedComponent = newComponents[dragIndex];

  //         // Remove the dragged component
  //         newComponents.splice(dragIndex, 1);
  //         // Insert it at the new position
  //         newComponents.splice(hoverIndex, 0, draggedComponent);

  //         console.log(
  //           "New component order:",
  //           newComponents.map((c) => c.name)
  //         );

  //         return {
  //           ...prevData,
  //           components: newComponents,
  //         };
  //       });
  //     },
  //     [setPageData]
  //   );
  // Global onChange handler for all component fields
  const handleGlobalFieldChange = async (
    index,
    field,
    value,
    extraData = {}
  ) => {
    if (onComponentFieldChange) {
      await onComponentFieldChange(index, field, value, extraData);
    }
  };

  // Draggable and droppable structure item component
  const StructureItem = ({ index, componentName, onRemove }) => {
    const ref = useRef(null);
    const componentData = pageData.components[index] || {};
    const [isCollapsed, setIsCollapsed] = useState(false);

    // useDrop hook for drop target
    const [{ isOver }, drop] = useDrop({
      accept: DND_TYPES.STRUCT_ITEM,
      hover: (item, monitor) => {
        if (!ref.current) {
          return;
        }

        const dragIndex = item.index;
        const hoverIndex = index;

        // Don't replace items with themselves
        if (dragIndex === hoverIndex) {
          return;
        }

        // Determine rectangle on screen
        const hoverBoundingRect = ref.current.getBoundingClientRect();

        // Get vertical middle
        const hoverMiddleY =
          (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

        // Determine mouse position
        const clientOffset = monitor.getClientOffset();
        if (!clientOffset) {
          return;
        }

        // Get pixels to the top
        const hoverClientY = clientOffset.y - hoverBoundingRect.top;

        // Only perform the move when the mouse has crossed half of the items height
        // When dragging downwards, only move when the cursor is below 50%
        // When dragging upwards, only move when the cursor is above 50%

        // Dragging downwards
        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return;
        }

        // Dragging upwards
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return;
        }

        // Time to actually perform the action
        moveComponent(dragIndex, hoverIndex);

        // Note: we're mutating the monitor item here!
        // Generally it's better to avoid mutations,
        // but it's good here for the sake of performance
        // to avoid expensive index searches.
        item.index = hoverIndex;
      },
      collect: (monitor) => ({
        isOver: monitor.isOver(),
      }),
    });

    // useDrag hook for drag source
    const [{ isDragging }, drag] = useDrag({
      type: DND_TYPES.STRUCT_ITEM,
      item: () => {
        console.log(`Starting drag for item at index: ${index}`);
        return { index };
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    // Connect drag and drop refs
    drag(drop(ref));

    return (
      <div
        ref={ref}
        className={`tw-bg-gray-100 tw-border tw-border-[#D9DBDF] tw-rounded-2xl tw-p-4 tw-transition-all tw-duration-200 tw-cursor-move ${
          isDragging
            ? "tw-shadow-lg tw-scale-105 tw-bg-blue-50"
            : isOver
            ? "tw-bg-yellow-50"
            : "tw-hover:tw-bg-gray-200"
        }`}
        style={{
          opacity: isDragging ? 0.8 : 1,
        }}
      >
        {/* Header with position and delete button */}
        <div className="tw-flex tw-items-center tw-justify-between">
          <div className="tw-flex tw-items-center tw-gap-3">
            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400" />
            <span className="tw-text-gray-600 tw-font-medium">
              Position {index + 1}
            </span>
          </div>
          <div className="tw-flex tw-items-center">
            <Button
              type="text"
              danger
              icon={<Trash2 className="tw-w-4 tw-h-4" />}
              onClick={(e) => {
                e.stopPropagation();
                onRemove(index);
              }}
              className=" tw-text-gray-400 tw-hover:tw-text-red-500 tw-transition-colors tw-rounded-lg tw-hover:tw-bg-white"
            />

            <Button
              type="text"
              icon={
                isCollapsed ? (
                  <ChevronDown className="tw-w-4 tw-h-4 tw-text-gray-400" />
                ) : (
                  <ChevronUp className="tw-w-4 tw-h-4 tw-text-gray-400" />
                )
              }
              onClick={(e) => {
                e.stopPropagation();
                setIsCollapsed(!isCollapsed);
              }}
              className="tw-w-full  tw-bg-transparent tw-border-0 tw-cursor-pointer"
            />
          </div>
        </div>
        {/* Collapsible Content */}
        {!isCollapsed && (
          <div className="tw-mt-4">
            {/* Version Dropdown */}
            <div className="tw-mb-4">
              <Select
                size="large"
                placeholder="V1"
                value={componentData.version || "v1"}
                onChange={(value) =>
                  handleGlobalFieldChange(index, "version", value)
                }
                onMouseDown={(e) => e.stopPropagation()}
                onFocus={(e) => e.stopPropagation()}
                className="tw-w-12 tw-h-3 tw-text-sm version-class"
                styles={{
                  optionFontSize: "5px",
                }}
                style={{
                  width: 55, // Adjust the width as needed
                  height: 30,
                  borderRadius: "100px", // Creates the pill shape
                  fontSize: "12px",
                }}
                options={[
                  { value: +"1", label: "V1" },
                  { value: +"2", label: "V2" },
                  { value: +"3", label: "V3" },
                ]}
              />
            </div>

            {/* Component Name Field */}
            <div className="tw-mb-4">
              <Input
                size="large"
                placeholder="Component Name"
                value={componentData.name || componentName || ""}
                onChange={(e) =>
                  handleGlobalFieldChange(index, "name", e.target.value)
                }
                onMouseDown={(e) => e.stopPropagation()}
                onFocus={(e) => e.stopPropagation()}
                className="tw-rounded-xl  tw-text-gray-900"
              />
            </div>

            <div className="tw-space-y-4">
              {/* CSS Class Input */}
              <div>
                <Input
                  size="large"
                  placeholder="Enter class"
                  value={componentData.cssClass || ""}
                  onChange={(e) => {
                    // handleCssChange(index, e.target.value);
                    handleGlobalFieldChange(index, "cssClass", e.target.value);
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  className="tw-rounded-xl  tw-text-gray-900"
                />
              </div>

              {/* Display Type Dropdown */}
              <div>
                <Select
                  size="large"
                  placeholder="Repeat"
                  value={componentData.repeator || "single"}
                  onChange={(value) => {
                    handleGlobalFieldChange(
                      index,
                      "repeator",
                      value,
                      value == "single" ? { repeatedComponentId: null } : {}
                    );
                  }}
                  onMouseDown={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  className="tw-w-full"
                  style={{
                    borderRadius: "16px",
                  }}
                  options={[
                    { value: "single", label: "Single" },
                    { value: "repeat", label: "Repeat" },
                  ]}
                />
              </div>
              {/* Display Which Component Select for repeat */}
              {componentData?.repeator == "repeat" && (
                <div>
                  <Select
                    size="large"
                    placeholder="Select Component"
                    value={componentData?.repeatedComponentId}
                    onChange={(value) => {
                      const repeatedComponent = components?.find(
                        (c) => c.id === value
                      );
                      const existingComp = components?.find(
                        (c) => c.id === componentData?.id
                      );
                      handleGlobalFieldChange(
                        index,
                        "repeatedComponentId",
                        value,
                        {
                          repeatedComponentName: autoDetectRepeatPlaceHolder(
                            existingComp?.html_content
                          ),
                          repeatedComponent: repeatedComponent,
                        }
                      );
                    }}
                    onMouseDown={(e) => e.stopPropagation()}
                    onFocus={(e) => e.stopPropagation()}
                    className="tw-w-full"
                    style={{
                      borderRadius: "16px",
                    }}
                    options={components?.map((comp) => ({
                      value: comp.id,
                      label: comp.name,
                    }))}
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Right Sidebar - Page Structure (collapsible, animated) */}
      {/* In-layout column with width animation */}
      <div
        className={`tw-bg-white tw-border-l tw-border-gray-200 tw-flex tw-flex-col tw-transition-all tw-duration-300 tw-ease-in-out ${
          isStructureOpen
            ? isMobile
              ? "tw-fixed tw-inset-0 tw-z-50 tw-w-full"
              : isTablet
              ? "tw-w-64"
              : "tw-w-[18rem]"
            : "tw-w-0 tw-overflow-hidden"
        } ${isMobile && isStructureOpen ? "tw-shadow-2xl" : ""}`}
      >
        {isStructureOpen && (
          <>
            <div className=" tw-border-b tw-border-gray-200 tw-flex tw-items-center tw-justify-between">
              <div className="tw-p-3 md:tw-p-4 tw-flex tw-items-start tw-justify-between">
                <div className="tw-flex tw-flex-col tw-justify-start tw-text-start">
                  <h3 className="tw-text-base md:tw-text-lg tw-font-semibold tw-text-gray-900">
                    Page Structure
                  </h3>
                  <p className="tw-text-sm tw-text-gray-600 tw-mt-1 tw-hidden md:tw-block">
                    {pageData?.components?.length || 0} components
                  </p>
                </div>
                {isMobile && (
                  <button
                    onClick={() => setIsStructureOpen(false)}
                    className="tw-p-2 tw-text-gray-400 tw-hover:tw-text-gray-600 tw-rounded-lg tw-ml-2"
                  >
                    <X className="tw-w-5 tw-h-5" />
                  </button>
                )}
              </div>
              <div className="tw-flex tw-h-full tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
                <Tooltip
                  title={
                    isStructureOpen
                      ? "Hide Page Structure"
                      : "Show Page Structure"
                  }
                >
                  <button
                    onClick={() => setIsStructureOpen((v) => !v)}
                    className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                  >
                    <ChevronLeft
                      size={30}
                      className={` ${isStructureOpen ? "" : "tw-rotate-180 "}`}
                    />
                  </button>
                </Tooltip>
              </div>
            </div>
            {/* Search Input */}
            <div className="tw-p-3 md:tw-p-4">
              <Input
                size="middle"
                placeholder="Search components..."
                prefix={
                  isSearching ? (
                    <Loader2 className="tw-w-4 tw-h-4 tw-text-blue-500 tw-animate-spin" />
                  ) : (
                    <Search className="tw-w-4 tw-h-8 tw-text-gray-400" />
                  )
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="tw-rounded-lg"
                allowClear
              />
            </div>
          </>
        )}

        <div className="tw-flex-1 tw-overflow-y-auto tw-p-3 md:tw-p-4 tw-pt-0">
          {pageData?.components?.length > 0 ? (
            <div className="tw-space-y-2">
              {pageData.components
                .map((pageComp, index) => {
                  const component = components?.find(
                    (c) => c.id === pageComp.id
                  );
                  return { pageComp, index, component };
                })
                .filter(({ component }) =>
                  debouncedSearchTerm
                    ? component?.name
                        ?.toLowerCase()
                        .includes(debouncedSearchTerm.toLowerCase())
                    : true
                )
                .map(({ pageComp, index, component }) => (
                  <StructureItem
                    key={
                      pageComp.uniqueId || `fallback-${pageComp.id}-${index}`
                    }
                    index={index}
                    componentName={component?.name}
                    onRemove={removeComponentFromPage}
                  />
                ))}
            </div>
          ) : (
            <div className="tw-text-center tw-py-8">
              <p className="tw-text-gray-500">No components added</p>
              <p className="tw-text-sm tw-text-gray-400 tw-mt-1">
                Components will appear here as you add them
              </p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default PageStructure;
