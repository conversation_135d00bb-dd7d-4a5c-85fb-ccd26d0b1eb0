import { Card, Radio } from "antd";
import { Braces, LayoutPanelTop, List } from "lucide-react";
import React, { useEffect, useState } from "react";
import DetailTab from "./DetailTab";
import StructureTab from "./StructureTab";
import TabList from "../../common/TabList";
import ContentTab from "./ContentTab";

const TemplateTabList = React.memo(
  ({
    pages,
    formData,
    setFormData,
    template,
    onCancel,
    handleSubmit,
    saving,
  }) => {
    const [previewMode, setPreviewMode] = useState("template_details");
    const tabContents = {
      template_details: {
        key: "template_details",
        label: "Template Details",
        icon: <List className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <DetailTab
            formData={formData}
            setFormData={setFormData}
            template={template}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            saving={saving}
          />
        ),
      },
      structure: {
        key: "structure",
        label: "Structure",
        icon: <LayoutPanelTop className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <StructureTab
            pages={pages}
            formData={formData}
            setFormData={setFormData}
            template={template}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            saving={saving}
          />
        ),
      },
      content: {
        key: "content",
        label: "Content",
        icon: <Braces className="tw-w-4 tw-h-4 tw-mr-2" />,
        content: (
          <ContentTab
            formData={formData}
            setFormData={setFormData}
            onCancel={onCancel}
            handleSubmit={handleSubmit}
            saving={saving}
            pages={pages}
          />
        ),
      },
    };

    const autoDetectPlaceholders = (content, currentFormData) => {
      // if (!content) return;
      const regex = /\$\{([^}]+)\}/g;
      const matches = [];
      let match;

      while ((match = regex.exec(content)) !== null) {
        if (!matches.includes(match[1])) {
          matches.push(match[1]);
        }
      }

      // Only update if placeholders have actually changed
      const newPlaceholders = [...new Set([...matches])];
      const currentPlaceholders = currentFormData.placeholders || [];

      if (
        JSON.stringify(newPlaceholders) !== JSON.stringify(currentPlaceholders)
      ) {
        setFormData((prevFormData) => ({
          ...prevFormData,
          placeholders: newPlaceholders,
        }));
      }
    };

    useEffect(() => {
      const content = formData?.html_content;
      if (!content) return;

      const timeout = setTimeout(() => {
        autoDetectPlaceholders(content, formData);
      }, 1000);

      return () => clearTimeout(timeout);
    }, [formData?.html_content]); // Remove formData from dependencies to prevent infinite loop
    return (
      <div className="tw-space-y-4">
        {/* Preview/Code Toggle */}
        <TabList
          tabContents={tabContents}
          setPreviewMode={setPreviewMode}
          previewMode={previewMode}
        />

        <div className="tw-space-y-4">
          {tabContents[previewMode]?.content ? (
            tabContents[previewMode]?.content
          ) : (
            <></>
          )}
        </div>
      </div>
    );
  }
);

export default TemplateTabList;
